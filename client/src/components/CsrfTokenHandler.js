import React, { useEffect, useState } from 'react';
import api from '../services/api';

/**
 * Component to handle CSRF token refreshing
 * This component doesn't render anything but ensures the CSRF token is refreshed
 * when the user logs in or when the token expires
 */
const CsrfTokenHandler = () => {
  const [refreshAttempts, setRefreshAttempts] = useState(0);
  const MAX_ATTEMPTS = 3;
  const RETRY_DELAY = 5000; // 5 seconds

  // Function to refresh the CSRF token
  const refreshCsrfToken = async (attempt = 0) => {
    try {
      console.log('Attempting CSRF token refresh, attempt:', attempt + 1);
      // Make a GET request to a dedicated endpoint that will set a new CSRF token
      // Use fetch instead of axios to avoid any axios configuration issues
      const response = await fetch('http://localhost:5000/api/csrf-token', {
        method: 'GET',
        credentials: 'include', // Include cookies
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      console.log('CSRF token refresh successful');
      setRefreshAttempts(0); // Reset attempts on success

      // Store the timestamp of the successful refresh
      localStorage.setItem('lastCsrfRefreshTime', Date.now().toString());

      // Also store the token in localStorage as a backup (will be used if cookie is lost)
      const token = document.cookie.split(';')
        .find(cookie => cookie.trim().startsWith('XSRF-TOKEN='));
      if (token) {
        const tokenValue = token.split('=')[1];
        localStorage.setItem('csrfToken', tokenValue);
      }
    } catch (error) {
      console.error('CSRF token refresh failed:', error.message);
      console.error('Error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        url: error.config?.url,
        method: error.config?.method
      });

      // Retry a few times with exponential backoff if we're not in the useEffect cleanup
      if (attempt < MAX_ATTEMPTS) {
        console.log(`Retrying CSRF token refresh in ${RETRY_DELAY * (attempt + 1)}ms...`);
        setTimeout(() => refreshCsrfToken(attempt + 1), RETRY_DELAY * (attempt + 1));
      } else {
        console.error('Max CSRF token refresh attempts reached');
        setRefreshAttempts(prev => prev + 1);
      }
    }
  };

  // Refresh the token when the component mounts (user logs in)
  useEffect(() => {
    // Initial token refresh
    refreshCsrfToken();

    // Set up a timer to refresh the token periodically (every 15 minutes)
    // This is shorter than the token expiry time (30 minutes) to ensure we always have a valid token
    const tokenRefreshInterval = setInterval(() => refreshCsrfToken(), 15 * 60 * 1000);

    // Also refresh the token when the user interacts with the page
    const handleUserInteraction = () => {
      // Only refresh if it's been at least 1 minute since the last refresh
      const lastRefreshTime = localStorage.getItem('lastCsrfRefreshTime');
      const now = Date.now();
      if (!lastRefreshTime || now - parseInt(lastRefreshTime) > 60000) {
        refreshCsrfToken();
        localStorage.setItem('lastCsrfRefreshTime', now.toString());
      }
    };

    // Add event listeners for user interaction
    document.addEventListener('click', handleUserInteraction);
    document.addEventListener('keydown', handleUserInteraction);

    // Clean up the interval and event listeners when the component unmounts
    return () => {
      clearInterval(tokenRefreshInterval);
      document.removeEventListener('click', handleUserInteraction);
      document.removeEventListener('keydown', handleUserInteraction);
    };
  }, []);

  // If we've had multiple refresh failures, try to recover by refreshing the page
  // This is a last resort to fix persistent CSRF issues
  useEffect(() => {
    if (refreshAttempts >= 3) {
      // Wait a moment before refreshing to avoid immediate refresh loops
      setTimeout(() => {
        window.location.reload();
      }, 5000);
    }
  }, [refreshAttempts]);

  // This component doesn't render anything
  return null;
};

export default CsrfTokenHandler;
