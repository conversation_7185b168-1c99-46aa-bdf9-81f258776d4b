const express = require('express');
const router = express.Router();
const csrf = require('csurf');
const crypto = require('crypto');

// Create a dedicated CSRF protection middleware for this route
const csrfProtection = csrf({
  // Use session-based storage (default) instead of cookies
  value: function (req) {
    // Look for the token in multiple places (in order of preference)
    return req.headers['x-csrf-token'] ||
           req.headers['x-xsrf-token'] ||
           req.body._csrf ||
           req.query._csrf;
  }
});

// Route to get a new CSRF token
router.get('/', csrfProtection, (req, res) => {
  try {
    // Generate a new token using csurf
    const token = req.csrfToken();

    // Send it in a cookie
    res.cookie('XSRF-TOKEN', token, {
      httpOnly: false, // Client-side JavaScript needs to read this
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
      maxAge: 30 * 60 * 1000 // 30 minutes for better security (was 24 hours)
    });

    // Also send it in the response header
    res.set('X-CSRF-Token', token);

    // Log success in development
    if (process.env.NODE_ENV !== 'production') {
      console.log('CSRF token generated successfully');
    }

    res.json({
      message: 'CSRF token refreshed successfully',
      success: true,
      csrfToken: token // Include token in response for localStorage backup
    });
  } catch (error) {
    console.error('Error generating CSRF token:', error);
    res.status(500).json({
      message: 'Failed to generate CSRF token',
      success: false
    });
  }
});

// Add a health check route that doesn't require CSRF
router.get('/health', (req, res) => {
  res.json({ status: 'ok' });
});

module.exports = router;
