const express = require('express');
const router = express.Router();
const csrf = require('csurf');
const crypto = require('crypto');

// Custom token generation function (same as in middleware)
const generateCsrfToken = () => {
  return crypto.randomBytes(32).toString('base64').replace(/[+/=]/g, (match) => {
    switch (match) {
      case '+': return '-';
      case '/': return '_';
      case '=': return '';
      default: return match;
    }
  });
};

// Route to get a new CSRF token (no csurf library needed)
router.get('/', (req, res) => {
  try {
    // Generate a new custom token
    const token = generateCsrfToken();

    // Send it in a cookie
    res.cookie('XSRF-TOKEN', token, {
      httpOnly: false, // Client-side JavaScript needs to read this
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
      maxAge: 30 * 60 * 1000 // 30 minutes for better security (was 24 hours)
    });

    // Also send it in the response header
    res.set('X-CSRF-Token', token);

    // Log success in development
    if (process.env.NODE_ENV !== 'production') {
      console.log('Custom CSRF token generated successfully');
    }

    res.json({
      message: 'CSRF token refreshed successfully',
      success: true,
      csrfToken: token // Include token in response for localStorage backup
    });
  } catch (error) {
    console.error('Error generating CSRF token:', error);
    res.status(500).json({
      message: 'Failed to generate CSRF token',
      success: false
    });
  }
});

// Add a health check route that doesn't require CSRF
router.get('/health', (req, res) => {
  res.json({ status: 'ok' });
});

module.exports = router;
