/**
 * Audit Logging Middleware
 *
 * Records detailed audit logs for all API requests and responses
 * to track user actions and system events.
 */
const logger = require('../utils/logger');
const { v4: uuidv4 } = require('uuid');

// Create a middleware to log all API requests
const auditLogMiddleware = (req, res, next) => {
  // Generate a unique request ID
  const requestId = uuidv4();
  req.requestId = requestId;

  // Get the start time
  const startTime = Date.now();

  // Extract user information from session (primary) or req.user (fallback)
  const user = req.session?.user || req.user;
  const userId = user?.id || 'unauthenticated';
  const userRole = user?.role || 'none';
  const userEmail = user?.email || 'unknown';

  // Extract API service information for external services
  const apiService = req.apiService || 'none';

  // Prepare request data for logging - with reduced verbosity
  const requestData = {
    requestId,
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userId,
    userRole,
    apiService,
  };

  // Only include these fields for non-GET requests or if in development mode
  if (req.method !== 'GET' || process.env.NODE_ENV === 'development') {
    // Add body for non-GET requests (with sanitization)
    if (Object.keys(req.body || {}).length > 0) {
      requestData.body = sanitizeRequestBody(req.body);
    }

    // Only include query params if they exist and are not empty
    if (Object.keys(req.query || {}).length > 0) {
      requestData.query = req.query;
    }

    // Only include route params if they exist and are not empty
    if (Object.keys(req.params || {}).length > 0) {
      requestData.params = req.params;
    }

    // Include user agent only in development mode
    if (process.env.NODE_ENV === 'development') {
      requestData.userAgent = req.headers['user-agent'];
    }
  }

  // Log the request
  logger.info(`API Request: ${req.method} ${req.originalUrl}`, {
    type: 'API_REQUEST',
    ...requestData,
  });

  // Capture the original end method
  const originalEnd = res.end;

  // Override the end method to log the response
  res.end = function(chunk, encoding) {
    // Calculate request duration
    const duration = Date.now() - startTime;

    // Restore the original end method
    res.end = originalEnd;

    // Call the original end method
    res.end(chunk, encoding);

    // Get the response status and data
    const statusCode = res.statusCode;
    const statusMessage = res.statusMessage;

    // Prepare response data for logging - with reduced verbosity
    const responseData = {
      requestId,
      statusCode,
      duration,
      userId,
    };

    // Only include these fields for errors or if in development mode
    if (statusCode >= 400 || process.env.NODE_ENV === 'development') {
      responseData.statusMessage = statusMessage;
      responseData.userRole = userRole;
      responseData.apiService = apiService;

      // Include detailed user info only for server errors
      if (statusCode >= 500) {
        responseData.userEmail = userEmail;
      }
    }

    // Determine log level based on status code
    let logLevel = 'info';
    if (statusCode >= 500) {
      logLevel = 'error';
    } else if (statusCode >= 400) {
      logLevel = 'warn';
    }

    // Log the response
    logger[logLevel](`API Response: ${statusCode} ${req.method} ${req.originalUrl}`, {
      type: 'API_RESPONSE',
      ...responseData,
    });
  };

  next();
};

/**
 * Sanitize request body to remove sensitive information
 */
function sanitizeRequestBody(body) {
  if (!body) return {};

  // Create a deep copy of the body
  const sanitized = JSON.parse(JSON.stringify(body));

  // List of sensitive fields to redact
  const sensitiveFields = [
    'password',
    'newPassword',
    'currentPassword',
    'confirmPassword',
    'token',
    'accessToken',
    'refreshToken',
    'apiKey',
    'secret',
    'creditCard',
    'ssn',
    'socialSecurityNumber',
  ];

  // Recursively sanitize the object
  function sanitizeObject(obj) {
    if (!obj || typeof obj !== 'object') return;

    Object.keys(obj).forEach(key => {
      if (sensitiveFields.includes(key.toLowerCase())) {
        obj[key] = '[REDACTED]';
      } else if (typeof obj[key] === 'object') {
        sanitizeObject(obj[key]);
      }
    });
  }

  sanitizeObject(sanitized);
  return sanitized;
}

module.exports = auditLogMiddleware;
