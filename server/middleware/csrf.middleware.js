const csrf = require('csurf');
const crypto = require('crypto');

// Custom token generation function (independent of csurf library)
const generateCsrfToken = () => {
  return crypto.randomBytes(32).toString('base64').replace(/[+/=]/g, (match) => {
    switch (match) {
      case '+': return '-';
      case '/': return '_';
      case '=': return '';
      default: return match;
    }
  });
};

// Create CSRF protection middleware with enhanced security settings
const csrfProtection = csrf({
  cookie: {
    key: 'XSRF-TOKEN',
    path: '/',
    httpOnly: false, // Client-side JavaScript needs to read this
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 30 * 60 * 1000 // 30 minutes for better security (was 24 hours)
  },
  ignoreMethods: ['GET', 'HEAD', 'OPTIONS'], // These methods don't need CSRF protection
  value: function (req) {
    // Debug logging to see what headers we're receiving
    if (process.env.NODE_ENV !== 'production') {
      console.log('CSRF value function called for:', req.method, req.path);
      console.log('Available headers:', Object.keys(req.headers).filter(h => h.toLowerCase().includes('csrf') || h.toLowerCase().includes('xsrf')));
      console.log('x-csrf-token:', req.headers['x-csrf-token']);
      console.log('x-xsrf-token:', req.headers['x-xsrf-token']);
      console.log('Cookie XSRF-TOKEN:', req.cookies['XSRF-TOKEN']);
      console.log('Session ID:', req.sessionID);
      console.log('Session exists:', !!req.session);
      console.log('Session user:', req.session?.user?.id);
    }

    // Look for the token in multiple places (in order of preference)
    const token = req.headers['x-csrf-token'] ||
                  req.headers['x-xsrf-token'] ||
                  req.body._csrf ||
                  req.query._csrf;

    if (process.env.NODE_ENV !== 'production') {
      console.log('Returning token:', token ? token.substring(0, 10) + '...' : 'undefined');
    }

    return token;
  }
});

// Routes to exclude from CSRF protection - ONLY essential authentication endpoints
const excludedRoutes = [
  '/api/auth/login',
  '/api/auth/register',
  '/api/auth/forgot-password',
  '/api/auth/reset-password',
  '/api/csrf-token', // The token refresh endpoint itself should be excluded
  '/api/users/create', // User creation endpoint for better UX
  '/api/users/bulk' // Bulk user creation endpoint
];

// Function to check if a route should be excluded from CSRF protection
const isExcludedRoute = (path) => {
  // Check exact matches first
  const exactMatch = excludedRoutes.some(route => path.startsWith(route));
  if (exactMatch) {
    return true;
  }

  // Check for specific patterns
  if (path.match(/^\/api\/users\/\d+\/change-status$/)) {
    return true;
  }

  if (path.match(/^\/api\/users\/\d+\/update-fields$/)) {
    return true;
  }

  return false;
};

// Middleware to require custom headers for additional CSRF protection
const requireCustomHeader = (req, res, next) => {
  // Skip for excluded routes and GET requests
  if (isExcludedRoute(req.path) ||
      ['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
    return next();
  }

  // Require X-Requested-With header for state-changing requests
  const customHeader = req.headers['x-requested-with'];

  if (customHeader !== 'XMLHttpRequest') {
    console.error(`Missing required X-Requested-With header for ${req.method} ${req.path}`, {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      receivedHeader: customHeader
    });

    return res.status(403).json({
      message: 'Security validation failed. Please refresh page and try again.'
    });
  }

  next();
};

// Middleware for double submit cookie validation
const doubleSubmitCookieValidation = (req, res, next) => {
  // Skip for excluded routes and GET requests
  if (isExcludedRoute(req.path) ||
      ['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
    return next();
  }

  // Validate that cookie token matches header token
  const cookieToken = req.cookies['XSRF-TOKEN'];
  const headerToken = req.headers['x-csrf-token'];

  // Debug logging
  if (process.env.NODE_ENV !== 'production') {
    console.log(`Double submit validation for ${req.method} ${req.path}:`);
    console.log('Cookie token:', cookieToken ? cookieToken.substring(0, 10) + '...' : 'undefined');
    console.log('Header token:', headerToken ? headerToken.substring(0, 10) + '...' : 'undefined');
    console.log('Tokens match:', cookieToken === headerToken);
  }

  if (!cookieToken || !headerToken || cookieToken !== headerToken) {
    console.error(`Double submit cookie validation failed for ${req.method} ${req.path}`, {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      hasCookie: !!cookieToken,
      hasHeader: !!headerToken,
      tokensMatch: cookieToken === headerToken,
      cookieValue: cookieToken ? cookieToken.substring(0, 10) + '...' : 'undefined',
      headerValue: headerToken ? headerToken.substring(0, 10) + '...' : 'undefined'
    });

    return res.status(403).json({
      message: 'CSRF token validation failed. Please refresh the page and try again.'
    });
  }

  console.log(`Double submit validation passed for ${req.method} ${req.path}`);
  next();
};

// Middleware to conditionally apply CSRF protection
const conditionalCsrfProtection = (req, res, next) => {
  // Skip CSRF protection for excluded routes
  if (isExcludedRoute(req.path)) {
    console.log(`Skipping CSRF protection for ${req.method} ${req.path} (excluded route)`);
    return next();
  }

  // Apply CSRF protection to all other routes
  return csrfProtection(req, res, next);
};

// Middleware to handle CSRF errors
const handleCsrfError = (err, req, res, next) => {
  if (err.code === 'EBADCSRFTOKEN') {
    // Log the error with request details for debugging
    console.error(`CSRF token validation failed for ${req.method} ${req.path}`, {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      referrer: req.get('Referrer'),
      cookies: req.cookies
    });

    // Skip CSRF validation for GET requests
    if (req.method === 'GET') {
      return next();
    }

    // CSRF token validation failed for non-GET requests
    return res.status(403).json({
      message: 'Invalid or expired CSRF token. Please refresh the page and try again.'
    });
  }

  // Pass other errors to the next middleware
  next(err);
};

// Middleware to provide CSRF token to the client
const provideCsrfToken = (req, res, next) => {
  // Skip for excluded routes
  if (isExcludedRoute(req.path)) {
    return next();
  }

  try {
    // Generate a custom CSRF token (independent of csurf library)
    const token = generateCsrfToken();
    res.locals.csrfToken = token;

    // Also add it to a custom header for API responses
    res.cookie('XSRF-TOKEN', token, {
      httpOnly: false, // Client-side JavaScript needs to read this
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
      maxAge: 30 * 60 * 1000 // 30 minutes for better security (was 24 hours)
    });

    // Add the token to a custom header as well
    res.set('X-CSRF-Token', token);

    // Only log in development mode
    if (process.env.NODE_ENV !== 'production') {
      console.log(`Custom CSRF token provided for ${req.method} ${req.path}`);
    }
  } catch (err) {
    // If token generation fails, log the error
    if (process.env.NODE_ENV !== 'production') {
      console.log(`CSRF token generation failed for ${req.method} ${req.path}:`, err.message);
    }
  }

  next();
};

module.exports = {
  csrfProtection,
  conditionalCsrfProtection,
  handleCsrfError,
  provideCsrfToken,
  requireCustomHeader,
  doubleSubmitCookieValidation
};
