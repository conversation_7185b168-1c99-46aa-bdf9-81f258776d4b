const axios = require('axios');

// Test script to verify CSRF and authentication fixes
async function testCsrfFix() {
  const baseURL = 'http://localhost:5000';
  
  // Create axios instance with credentials
  const api = axios.create({
    baseURL,
    withCredentials: true,
    headers: {
      'Content-Type': 'application/json',
      'X-Requested-With': 'XMLHttpRequest'
    }
  });

  try {
    console.log('🧪 Testing CSRF and Authentication Fix...\n');

    // Step 1: Login first
    console.log('1. Attempting login...');
    const loginResponse = await api.post('/api/auth/login', {
      email: '<EMAIL>', // Replace with actual admin credentials
      password: 'Password1'
    });
    
    console.log('✅ Login successful');
    console.log('Session established:', !!loginResponse.headers['set-cookie']);

    // Step 2: Get CSRF token
    console.log('\n2. Getting CSRF token...');
    const csrfResponse = await api.get('/api/csrf-token');
    console.log('✅ CSRF token obtained');

    // Extract CSRF token from cookie
    const cookies = csrfResponse.headers['set-cookie'] || [];
    let csrfToken = null;
    
    for (const cookie of cookies) {
      if (cookie.startsWith('XSRF-TOKEN=')) {
        csrfToken = cookie.split('=')[1].split(';')[0];
        break;
      }
    }

    if (!csrfToken) {
      throw new Error('CSRF token not found in cookies');
    }

    console.log('CSRF token extracted:', csrfToken.substring(0, 10) + '...');

    // Step 3: Test subjects API with CSRF token
    console.log('\n3. Testing subjects API...');
    
    // Add CSRF token to headers
    api.defaults.headers['X-CSRF-Token'] = csrfToken;

    // Test GET request (should work without CSRF)
    const getResponse = await api.get('/api/subjects');
    console.log('✅ GET /api/subjects successful, found', getResponse.data.length, 'subjects');

    // Test POST request (requires CSRF)
    console.log('\n4. Testing POST request with CSRF token...');
    const testSubject = {
      name: 'Test Subject ' + Date.now(),
      code: 'TEST' + Date.now(),
      description: 'Test subject for CSRF validation',
      isActive: true
    };

    const postResponse = await api.post('/api/subjects', testSubject);
    console.log('✅ POST /api/subjects successful');
    console.log('Created subject:', postResponse.data.name);

    // Clean up - delete the test subject
    if (postResponse.data.id) {
      await api.delete(`/api/subjects/${postResponse.data.id}`);
      console.log('✅ Test subject cleaned up');
    }

    console.log('\n🎉 All tests passed! CSRF and authentication are working correctly.');

  } catch (error) {
    console.error('\n❌ Test failed:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Message:', error.response.data?.message || error.response.statusText);
      console.error('Headers:', error.response.headers);
    } else {
      console.error('Error:', error.message);
    }
  }
}

// Run the test
testCsrfFix();
